import * as THREE from 'three';
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';
import { TextGeometry } from 'three/examples/jsm/geometries/TextGeometry.js';
import { SceneManager } from '../SceneManager';
import { ScrollTracker, type ScrollData, lerp, createRandomColor } from '../utils';

interface Primitive {
	mesh: THREE.Mesh;
	originalPosition: THREE.Vector3;
	rotationSpeed: THREE.Vector3;
	floatOffset: number;
	floatSpeed: number;
}

export class RotatingPrimitivesScene {
	private sceneManager: SceneManager;
	private scrollTracker: ScrollTracker;
	private primitives: Primitive[] = [];
	private ambientLight: THREE.AmbientLight;
	private directionalLight: THREE.DirectionalLight;
	private fontLoader: FontLoader;
	private textMesh: THREE.Mesh | null = null;
	private time = 0;

	constructor(canvas: HTMLCanvasElement) {
		this.sceneManager = new SceneManager({
			canvas,
			alpha: true,
			antialias: true
		});

		this.scrollTracker = new ScrollTracker();
		this.fontLoader = new FontLoader();
		this.setupLights();
		this.createHelpers();
		this.createPrimitives();
		this.createPlane();
		this.create3DText();
		// this.setupScrollInteraction();
		this.startAnimation();
	}

	private createHelpers(): void {
		const gridHelper = new THREE.GridHelper(10, 10);
		this.sceneManager.addToScene(gridHelper);
	}

	private createPlane(): void {
		// Create a wireframe plane
		const geometry = new THREE.PlaneGeometry(40, 40, 40, 40); // More segments = more grid lines
		const wireframe = new THREE.WireframeGeometry(geometry);

		const lineMaterial = new THREE.LineBasicMaterial({ color: 0xff00ff }); // vaporwave pink
		const plane = new THREE.LineSegments(wireframe, lineMaterial);
		plane.rotation.x = -Math.PI / 2; // Rotate to lay flat

		this.sceneManager.addToScene(plane);
	}

	private async create3DText(): Promise<void> {
		try {
			// Load a font (using Three.js built-in font)
			const font = await this.fontLoader.loadAsync('https://threejs.org/examples/fonts/helvetiker_regular.typeface.json');

			// Create text geometry
			let textGeometry = new TextGeometry('Creative Developer', {
				font: font,
				size: 0.8,
				height: 0.2,
				curveSegments: 12,
				bevelEnabled: true,
				bevelThickness: 0.03,
				bevelSize: 0.02,
				bevelOffset: 0,
				bevelSegments: 5
			});

			// Center the text geometry
			textGeometry.computeBoundingBox();
			const textWidth = textGeometry.boundingBox!.max.x - textGeometry.boundingBox!.min.x;
			const textHeight = textGeometry.boundingBox!.max.y - textGeometry.boundingBox!.min.y;
			textGeometry.translate(-textWidth / 2, -textHeight / 2, 0);

			// Create material with a nice color
			let textMaterial = new THREE.MeshLambertMaterial({
				color: 0x3b82f6, // Blue color
				transparent: true,
				opacity: 0.9
			});

			// Create mesh and position it
			this.textMesh = new THREE.Mesh(textGeometry, textMaterial);
			this.textMesh.position.set(0, 5, -10);

			// Add to scene
			this.sceneManager.addToScene(this.textMesh);

			console.log('3D text "Creative Developer" created successfully');


			textGeometry = new TextGeometry('Vaporwave', {
				font: font,
				size: 5,
				height: 2,
				curveSegments: 12,
				bevelEnabled: true,
				bevelThickness: 0.2,
				bevelSize: 0.3,
				bevelSegments: 5
			});

			textGeometry.center();

			textMaterial = new THREE.MeshStandardMaterial({
				color: 0xff00ff,
				metalness: 0.8,
				roughness: 0.2
			});

			this.textMesh = new THREE.Mesh(textGeometry, textMaterial);

			this.sceneManager.addToScene(this.textMesh);

		} catch (error) {
			console.error('Error loading font or creating 3D text:', error);
			// Fallback: create a simple text placeholder using a basic geometry
			this.createFallbackText();
		}
	}

	private createFallbackText(): void {
		// Simple fallback using a box geometry as placeholder
		const geometry = new THREE.BoxGeometry(4, 0.5, 0.2);
		const material = new THREE.MeshLambertMaterial({
			color: 0x3b82f6,
			transparent: true,
			opacity: 0.8
		});

		this.textMesh = new THREE.Mesh(geometry, material);
		this.textMesh.position.set(0, 5, -10);
		this.sceneManager.addToScene(this.textMesh);

		console.log('Fallback text placeholder created');
	}

	private setupLights(): void {
		// Ambient light for overall illumination
		this.ambientLight = new THREE.AmbientLight(0xF6F6F6, 0.9);
		this.sceneManager.addToScene(this.ambientLight);

		// Directional light for shadows and depth
		this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
		this.directionalLight.position.set(5, 5, 5);
		this.directionalLight.castShadow = true;
		this.sceneManager.addToScene(this.directionalLight);
	}

	private createPrimitives(): void {
		const geometries = [
			new THREE.BoxGeometry(1, 1, 1),
			new THREE.SphereGeometry(0.6, 32, 32),
			new THREE.ConeGeometry(0.6, 1.2, 8),
			new THREE.CylinderGeometry(0.5, 0.5, 1, 8),
			new THREE.OctahedronGeometry(0.7),
			new THREE.TetrahedronGeometry(0.8),
			new THREE.DodecahedronGeometry(0.6),
			new THREE.IcosahedronGeometry(0.7)
		];

		const numPrimitives = 15;

		for (let i = 0; i < numPrimitives; i++) {
			const geometry = geometries[Math.floor(Math.random() * geometries.length)];
			const material = new THREE.MeshLambertMaterial({
				color: createRandomColor(),
				transparent: true,
				opacity: 0.8
			});

			const mesh = new THREE.Mesh(geometry, material);

			// Random position
			const x = (Math.random() - 0.5) * 20;
			const y = (Math.random() - 0.5) * 15;
			const z = Math.random() * -10;

			mesh.position.set(x, y, z);

			// Random scale
			const scale = 0.5 + Math.random() * 1.5;
			mesh.scale.setScalar(scale);

			const primitive: Primitive = {
				mesh,
				originalPosition: mesh.position.clone(),
				rotationSpeed: new THREE.Vector3(
					(Math.random() - 0.5) * 0.02,
					(Math.random() - 0.5) * 0.02,
					(Math.random() - 0.5) * 0.02
				),
				floatOffset: Math.random() * Math.PI * 2,
				floatSpeed: 0.5 + Math.random() * 0.5
			};

			this.primitives.push(primitive);
			this.sceneManager.addToScene(mesh);
		}

		// Position camera
		this.sceneManager.camera.position.set(0, 0, 0);
		this.sceneManager.camera.lookAt(0, 0, 0);
		this.sceneManager.camera.position.y = 5;
		this.sceneManager.controls.update();
	}

	private setupScrollInteraction(): void {
		this.scrollTracker.addCallback((data: ScrollData) => {
			// Move camera based on scroll
			const targetY = data.scrollProgress * 5;
			// this.sceneManager.camera.position.y = lerp(
			// 	this.sceneManager.camera.position.y,
			// 	targetY,
			// 	0.5
			// );

			this.sceneManager.camera.position.z = lerp(
				this.sceneManager.camera.position.z,
				targetY * 20,
				0.1
			);

			// Rotate primitives based on scroll
			this.primitives.forEach((primitive, index) => {
				const scrollRotation = data.scrollProgress * Math.PI * 2;
				const offset = (index / this.primitives.length) * Math.PI * 2;

				primitive.mesh.rotation.y = scrollRotation + offset;

				// Move primitives slightly based on scroll
				const scrollOffset = data.scrollProgress * 2;
				primitive.mesh.position.x = primitive.originalPosition.x + Math.sin(scrollRotation + offset) * scrollOffset;
				primitive.mesh.position.z = primitive.originalPosition.z + Math.cos(scrollRotation + offset) * scrollOffset;
			});
		});
	}

	private startAnimation(): void {
		this.sceneManager.startAnimation((time: number) => {
			this.time = time * 0.001; // Convert to seconds

			// Animate primitives
			// this.primitives.forEach((primitive) => {
			// 	// Continuous rotation
			// 	primitive.mesh.rotation.x += primitive.rotationSpeed.x;
			// 	primitive.mesh.rotation.y += primitive.rotationSpeed.y;
			// 	primitive.mesh.rotation.z += primitive.rotationSpeed.z;
			//
			// 	// Floating animation
			// 	const floatY = Math.sin(this.time * primitive.floatSpeed + primitive.floatOffset) * 0.5;
			// 	primitive.mesh.position.y = primitive.originalPosition.y + floatY;
			//
			// 	// Subtle pulsing scale
			// 	const pulseScale = 1 + Math.sin(this.time * 2 + primitive.floatOffset) * 0.1;
			// 	primitive.mesh.scale.setScalar(primitive.mesh.scale.x * pulseScale);
			// });

			// Animate lights
			this.directionalLight.position.x = Math.sin(this.time * 0.5) * 5;
			this.directionalLight.position.z = Math.cos(this.time * 0.5) * 5;

			// // Animate text if it exists
			// if (this.textMesh) {
			// 	// Gentle rotation around Y axis
			// 	this.textMesh.rotation.y = Math.sin(this.time * 0.3) * 0.1;
			//
			// 	// Subtle floating animation
			// 	this.textMesh.position.y = 5 + Math.sin(this.time * 0.8) * 0.2;
			//
			// 	// Slight scale pulsing
			// 	const scale = 1 + Math.sin(this.time * 1.2) * 0.05;
			// 	this.textMesh.scale.setScalar(scale);
			// }
		});
	}

	public destroy(): void {
		this.sceneManager.destroy();
		this.scrollTracker.destroy();

		// Dispose of geometries and materials
		this.primitives.forEach((primitive) => {
			primitive.mesh.geometry.dispose();
			if (Array.isArray(primitive.mesh.material)) {
				primitive.mesh.material.forEach(material => material.dispose());
			} else {
				primitive.mesh.material.dispose();
			}
		});

		// Dispose of text mesh
		if (this.textMesh) {
			this.textMesh.geometry.dispose();
			if (Array.isArray(this.textMesh.material)) {
				this.textMesh.material.forEach(material => material.dispose());
			} else {
				this.textMesh.material.dispose();
			}
		}
	}
}
